package com.facishare.fmcg.provider.license;

import com.google.common.base.Strings;
import org.apache.commons.collections4.MapUtils;

import java.util.HashMap;
import java.util.Map;

public class ModuleLicenseHandlerFactory {

    private static final Map<String, IModuleLicenseHandler> handlerMap = new HashMap<>();

    static {
        Map<String, IModuleLicenseHandler> licenseHandlerMap = SpringContextHolder.getBeansOfType(IModuleLicenseHandler.class);
        if (MapUtils.isNotEmpty(licenseHandlerMap)) {
            licenseHandlerMap.values().forEach(handler -> handlerMap.put(handler.getAppCode(), handler));
        }
    }

    public static Map<String, IModuleLicenseHandler> getAllHandlerMap() {
        return handlerMap;
    }

    public IModuleLicenseHandler getCenter(String key) {
        return handlerMap.get(key);
    }
}