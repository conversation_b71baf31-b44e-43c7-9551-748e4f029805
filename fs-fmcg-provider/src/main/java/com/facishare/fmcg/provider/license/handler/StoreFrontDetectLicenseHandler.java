package com.facishare.fmcg.provider.license.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.api.dto.abstraction.ApiArg;
import com.facishare.fmcg.api.dto.ai.model.AIDetectRuleDTO;
import com.facishare.fmcg.api.dto.ai.model.AddModel;
import com.facishare.fmcg.api.dto.ai.model.FieldDTO;
import com.facishare.fmcg.api.dto.ai.model.ModelDTO;
import com.facishare.fmcg.api.dto.ai.model.SaveOrUpdateDetectRule;
import com.facishare.fmcg.api.dto.ai.model.TokenInfoDTO;
import com.facishare.fmcg.api.service.ai.model.AIModelService;
import com.facishare.fmcg.provider.dao.abstraction.PromptTemplateDAO;
import com.facishare.fmcg.provider.dao.po.PromptTemplatePO;
import com.facishare.fmcg.provider.license.ModuleLicenseHandlerBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component("storeFrontDetectLicenseHandler")
public class StoreFrontDetectLicenseHandler extends ModuleLicenseHandlerBase {

    @Resource
    private PromptTemplateDAO promptTemplateDAO;

    @Resource
    private AIModelService aiModelService;

    private static final String STORE_FRONT_PROMPT_TEMPLATE_CODE = "STORE_RECOGNITION_TEMPLATE";
    private static final String STORE_FRONT_NAME_MATCH_TEMPLATE_CODE = "STORE_RECOGNITION_AND_NAME_MATCH_TEMPLATE";
    private static final String STORE_FRONT_MODEL_NAME = "门头照识别";
    private static final String STORE_FRONT_AI_RULE_NAME_1 = "新建/编辑门店识别门头照信息";
    private static final String STORE_FRONT_AI_RULE_NAME_2 = "外勤拍门头验真";

    @Override
    public String getAppCode() {
        return "FMCG.STORE_FRONT_DETECT";
    }

    @Override
    public String formatOwnerCode(int tenantId, String tenantAccount, int userId) {
        return tenantId + "." + userId;
    }

    @Override
    public void active(int tenantId, String tenantAccount, int userId) {
        log.info("开始激活门头照识别功能，tenantId: {}, userId: {}", tenantId, userId);

        try {
            // 1. 初始化提示词模板（2个模板）
            initPromptTemplates(tenantId, userId);

            // 2. 初始化门头照识别模型
            String modelId = initStoreFrontModel(tenantId, userId);

            // 3. 初始化AI规则，默认开启门头照识别
            initAIRule(tenantId, userId, modelId);

            log.info("门头照识别功能激活完成，tenantId: {}", tenantId);
        } catch (Exception e) {
            log.error("激活门头照识别功能失败，tenantId: {}, userId: {}", tenantId, userId, e);
            throw new RuntimeException("激活门头照识别功能失败", e);
        }

        super.active(tenantId, tenantAccount, userId);
    }

    /**
     * 初始化提示词模板（2个模板）
     */
    private void initPromptTemplates(int tenantId, int userId) {
        try {
            // 1. 初始化基础门店识别提示词模板
            initSinglePromptTemplate(tenantId, userId,
                "model/storeFrontDetectPrompt.json",
                STORE_FRONT_PROMPT_TEMPLATE_CODE,
                "门店识别提示词模板");

            // 2. 初始化门店识别以及店名匹配提示词模板
            initSinglePromptTemplate(tenantId, userId,
                "model/storeFrontDetectAndNameMatchPrompt.json",
                STORE_FRONT_NAME_MATCH_TEMPLATE_CODE,
                "门店识别以及店名匹配提示词模板");

        } catch (Exception e) {
            log.error("初始化提示词模板失败，tenantId: {}", tenantId, e);
            throw new RuntimeException("初始化提示词模板失败", e);
        }
    }

    /**
     * 初始化单个提示词模板
     */
    private void initSinglePromptTemplate(int tenantId, int userId, String configPath, String templateCode, String templateName) {
        try {
            // 检查是否已存在提示词模板
            PromptTemplatePO existingTemplate = promptTemplateDAO.getByCode(tenantId,templateCode);
            if (existingTemplate != null) {
                log.info("{}已存在，无需创建，tenantId: {}", templateName, tenantId);
                return;
            }

            // 从配置文件读取默认模板配置
            PromptTemplatePO templatePO = loadPromptTemplateFromConfig(configPath, templateCode);
            if (templatePO == null) {
                log.error("无法从配置文件加载{}，tenantId: {}", templateName, tenantId);
                return;
            }

            // 设置租户和用户信息
            templatePO.setTenantId(-1);  // 全局模板
            templatePO.setCreator(-10000);  // 系统用户
            templatePO.setLastUpdater(-10000);

            // 保存模板
            String templateId = promptTemplateDAO.save(templatePO);
            log.info("成功创建{}，tenantId: {}, templateId: {}", templateName, tenantId, templateId);

        } catch (Exception e) {
            log.error("初始化{}失败，tenantId: {}", templateName, tenantId, e);
            throw new RuntimeException("初始化" + templateName + "失败", e);
        }
    }

    /**
     * 初始化门头照识别模型
     */
    private String initStoreFrontModel(int tenantId, int userId) {
        try {
            // 检查是否已存在同名模型（简单检查，实际项目中可能需要更复杂的逻辑）
            log.info("开始创建门头照识别模型，tenantId: {}", tenantId);


            // 创建门头照识别模型
            ModelDTO modelDTO = createStoreFrontModelDTO(tenantId, userId);

            ApiArg<AddModel.Arg> addModelArg = new ApiArg<>();
            addModelArg.setTenantId(tenantId);
            addModelArg.setUserId(userId);

            AddModel.Arg arg = new AddModel.Arg();
            arg.setModel(modelDTO);
            addModelArg.setData(arg);

            AddModel.Result result = aiModelService.addModel(addModelArg).getData();
            if (result != null && result.getModel() != null) {
                String modelId = result.getModel().getId();
                log.info("成功创建门头照识别模型，tenantId: {}, modelId: {}", tenantId, modelId);
                return modelId;
            } else {
                log.error("创建门头照识别模型失败，返回结果为空，tenantId: {}", tenantId);
                throw new RuntimeException("创建门头照识别模型失败");
            }

        } catch (Exception e) {
            log.error("初始化门头照识别模型失败，tenantId: {}", tenantId, e);
            throw new RuntimeException("初始化门头照识别模型失败", e);
        }
    }

    /**
     * 创建门头照识别模型DTO
     */
    private ModelDTO createStoreFrontModelDTO(int tenantId, int userId) {
        //todo：如果已经存在门头模型就不进行预置
        ModelDTO modelDTO = new ModelDTO();
        modelDTO.setTenantId(tenantId);
        modelDTO.setName(STORE_FRONT_MODEL_NAME);
        modelDTO.setScene("storeFront");
        modelDTO.setType("storeFrontDetect");
        modelDTO.setParentType("LLM");
        modelDTO.setStatus(1);
        modelDTO.setPlatform("qwen_vl");
        modelDTO.setModelManufacturer("fx");
        modelDTO.setDescription("通过纷享自研图像模型快速识别创建门店拍摄的门头照片，提取门头照内容信息");

        // 设置参数
        JSONObject params = new JSONObject();
        modelDTO.setParams(params);

        modelDTO.setTokenInfo(null);

        // 设置AI规则能力
        List<String> capabilities = new ArrayList<>();
        capabilities.add("isOpenStorefrontDetect");
        modelDTO.setAiRuleCapabilities(capabilities);

        return modelDTO;
    }

    /**
     * 从配置文件加载提示词模板
     */
    private PromptTemplatePO loadPromptTemplateFromConfig(String configPath, String templateCode) {
        try {
            ClassPathResource resource = new ClassPathResource(configPath);
            if (!resource.exists()) {
                log.error("配置文件不存在: {}", configPath);
                return null;
            }

            // 读取配置文件内容
            byte[] bytes = new byte[(int) resource.contentLength()];
            resource.getInputStream().read(bytes);
            String jsonContent = new String(bytes, StandardCharsets.UTF_8);

            // 解析JSON配置
            PromptTemplatePO templatePO = JSON.parseObject(jsonContent, PromptTemplatePO.class);

            // 设置正确的模板代码（覆盖JSON中的code字段）
            templatePO.setCode(templateCode);

            log.info("成功从配置文件加载提示词模板: {}, code: {}", templatePO.getName(), templateCode);
            return templatePO;

        } catch (IOException e) {
            log.error("读取配置文件失败: {}", configPath, e);
            return null;
        } catch (Exception e) {
            log.error("解析配置文件失败: {}", configPath, e);
            return null;
        }
    }

    /**
     * 初始化AI规则，默认开启门头照识别
     */
    private void initAIRule(int tenantId, int userId, String modelId) {
        try {
            // 创建门头照识别AI规则
            AIDetectRuleDTO ruleDTO = createStoreFrontDetectAIRuleDTO(tenantId, userId, modelId);

            ApiArg<SaveOrUpdateDetectRule.Arg> saveRuleArg = new ApiArg<>();
            saveRuleArg.setTenantId(tenantId);
            saveRuleArg.setUserId(userId);

            SaveOrUpdateDetectRule.Arg arg = new SaveOrUpdateDetectRule.Arg();
            arg.setAiDetectRuleDTO(ruleDTO);
            saveRuleArg.setData(arg);

            SaveOrUpdateDetectRule.Result result = aiModelService.saveOrUpdateDetectRule(saveRuleArg).getData();
            if (result != null && result.getRule() != null) {
                String ruleId = result.getRule().getId();
                log.info("成功创建门头照识别AI规则，tenantId: {}, ruleId: {}", tenantId, ruleId);
            } else {
                log.error("创建门头照识别AI规则失败，返回结果为空，tenantId: {}", tenantId);
                throw new RuntimeException("创建门头照识别AI规则失败");
            }

        } catch (Exception e) {
            log.error("初始化门头照识别AI规则失败，tenantId: {}", tenantId, e);
            throw new RuntimeException("初始化门头照识别AI规则失败", e);
        }
    }

    /**
     * 创建门头照识别AI规则DTO1
     */
    private AIDetectRuleDTO createStoreFrontDetectAIRuleDTO(int tenantId, int userId, String modelId) {
        AIDetectRuleDTO ruleDTO = new AIDetectRuleDTO();
        ruleDTO.setTenantId(tenantId);
        ruleDTO.setName(STORE_FRONT_AI_RULE_NAME_1);
        ruleDTO.setApiName("StoreFrontDetect");
        ruleDTO.setPromptTemplate(STORE_FRONT_PROMPT_TEMPLATE_CODE);
        ruleDTO.setModelId(modelId);
        ruleDTO.setDefault(true);
        ruleDTO.setRuleDescribe("业务员开拓新店，拍摄门头照通过AI验真门头照及自动录入门店信息");
        ruleDTO.setMasterDescribeApiName("AccountObj");

        // 设置检测能力映射
        Map<String, Integer> detectCapabilityMap = new HashMap<>();
        detectCapabilityMap.put("isOpenStorefrontDetect", 1);
        ruleDTO.setDetectCapabilityMap(detectCapabilityMap);

        // 设置字段映射
        Map<String, FieldDTO> fieldMap = new HashMap<>();

        // 门店名称字段
        FieldDTO storeNameField = new FieldDTO();
        storeNameField.setType("mapping");
        storeNameField.setFieldKey("storeName");
        storeNameField.setObjectApiName("StoreFrontDetectObj");
        storeNameField.setFieldType("text");
        storeNameField.setAiStoreFieldApiName("store_name_ai");
        storeNameField.setManuallyStoreFieldApiName("name");
        fieldMap.put("storeName", storeNameField);

        // 是否门头照字段
        FieldDTO isStorefrontField = new FieldDTO();
        isStorefrontField.setType("mapping");
        isStorefrontField.setFieldKey("isStorefront");
        isStorefrontField.setObjectApiName("StoreFrontDetectObj");
        isStorefrontField.setFieldType("true_or_false");
        isStorefrontField.setAiStoreFieldApiName("store_name_ai_match");
        fieldMap.put("isStorefront", isStorefrontField);

        ruleDTO.setFieldMap(fieldMap);

        return ruleDTO;
    }

    /**
     * 创建门头照识别AI规则DTO2
     */
    private AIDetectRuleDTO createStoreFrontDetectAndMatchAIRuleDTO(int tenantId, int userId, String modelId) {
        AIDetectRuleDTO ruleDTO = new AIDetectRuleDTO();
        ruleDTO.setTenantId(tenantId);
        ruleDTO.setName(STORE_FRONT_AI_RULE_NAME_2);
        ruleDTO.setApiName("StoreFrontDetectAndMatchName");
        ruleDTO.setPromptTemplate(STORE_FRONT_NAME_MATCH_TEMPLATE_CODE);
        ruleDTO.setModelId(modelId);
        ruleDTO.setDefault(true);
        ruleDTO.setRuleDescribe("业务员日常巡店，拍门头照打卡通过AI验真");

        // 设置检测能力映射
        Map<String, Integer> detectCapabilityMap = new HashMap<>();
        detectCapabilityMap.put("isOpenStorefrontDetect", 1);
        ruleDTO.setDetectCapabilityMap(detectCapabilityMap);

        // 设置字段映射
        Map<String, FieldDTO> fieldMap = new HashMap<>();


        ruleDTO.setFieldMap(fieldMap);

        return ruleDTO;
    }
}