package com.facishare.fmcg.provider.license.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.fmcg.provider.dao.abstraction.PromptTemplateDAO;
import com.facishare.fmcg.provider.dao.po.PromptTemplatePO;
import com.facishare.fmcg.provider.license.ModuleLicenseHandlerBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component("storeFrontDetectLicenseHandler")
public class StoreFrontDetectLicenseHandler extends ModuleLicenseHandlerBase {

    @Resource
    private PromptTemplateDAO promptTemplateDAO;

    private static final String STORE_FRONT_PROMPT_TEMPLATE_CODE = "STORE_RECOGNITION_TEMPLATE";

    @Override
    public String getAppCode() {
        return "FMCG.STORE_FRONT_DETECT";
    }

    @Override
    public String formatOwnerCode(int tenantId, String tenantAccount, int userId) {
        return tenantId + "." + userId;
    }

    @Override
    public void active(int tenantId, String tenantAccount, int userId) {
        log.info("开始激活门头照识别功能，tenantId: {}, userId: {}", tenantId, userId);

        try {
            // 初始化提示词模板
            initPromptTemplate(tenantId, userId);

            log.info("门头照识别功能激活完成，tenantId: {}", tenantId);
        } catch (Exception e) {
            log.error("激活门头照识别功能失败，tenantId: {}, userId: {}", tenantId, userId, e);
            throw new RuntimeException("激活门头照识别功能失败", e);
        }

        super.active(tenantId, tenantAccount, userId);
    }

    /**
     * 初始化提示词模板
     */
    private void initPromptTemplate(int tenantId, int userId) {
        try {
            // 检查是否已存在提示词模板
            PromptTemplatePO existingTemplate = promptTemplateDAO.getByCode(STORE_FRONT_PROMPT_TEMPLATE_CODE);
            if (existingTemplate != null) {
                log.info("门店识别提示词模板已存在，无需创建，tenantId: {}", tenantId);
                return;
            }

            // 从配置文件读取默认模板配置
            PromptTemplatePO templatePO = loadPromptTemplateFromConfig();
            if (templatePO == null) {
                log.error("无法从配置文件加载提示词模板，tenantId: {}", tenantId);
                return;
            }

            // 设置租户和用户信息
            templatePO.setTenantId(tenantId);
            templatePO.setCreator(userId);
            templatePO.setLastUpdater(userId);

            // 保存模板
            String templateId = promptTemplateDAO.save(templatePO);
            log.info("成功创建门店识别提示词模板，tenantId: {}, templateId: {}", tenantId, templateId);

        } catch (Exception e) {
            log.error("初始化提示词模板失败，tenantId: {}", tenantId, e);
            throw new RuntimeException("初始化提示词模板失败", e);
        }
    }

    /**
     * 从配置文件加载提示词模板
     */
    private PromptTemplatePO loadPromptTemplateFromConfig() {
        try {
            ClassPathResource resource = new ClassPathResource("model/storeFrontDetectPrompt.json");
            if (!resource.exists()) {
                log.error("配置文件不存在: model/storeFrontDetectPrompt.json");
                return null;
            }

            // 读取配置文件内容
            byte[] bytes = new byte[(int) resource.contentLength()];
            resource.getInputStream().read(bytes);
            String jsonContent = new String(bytes, StandardCharsets.UTF_8);

            // 解析JSON配置
            JSONObject config = JSON.parseObject(jsonContent);

            // 创建提示词模板对象
            PromptTemplatePO templatePO = new PromptTemplatePO();
            templatePO.setName(config.getString("name"));
            templatePO.setCode(config.getString("code"));
            templatePO.setPromptText(config.getString("promptText"));
            templatePO.setModelType(config.getString("modelType"));

            // 设置输入参数
            if (config.containsKey("IP") && config.getJSONArray("IP") != null) {
                List<PromptTemplatePO.ParameterDefinition> inputParams =
                    JSON.parseArray(config.getJSONArray("IP").toJSONString(),
                                  PromptTemplatePO.ParameterDefinition.class);
                templatePO.setInputParameters(inputParams);
            } else {
                templatePO.setInputParameters(new ArrayList<>());
            }

            // 设置输出参数
            if (config.containsKey("OP") && config.getJSONArray("OP") != null) {
                List<PromptTemplatePO.ParameterDefinition> outputParams =
                    JSON.parseArray(config.getJSONArray("OP").toJSONString(),
                                  PromptTemplatePO.ParameterDefinition.class);
                templatePO.setOutputParameters(outputParams);
            } else {
                templatePO.setOutputParameters(new ArrayList<>());
            }

            templatePO.setDeleted(config.getBooleanValue("deleted"));

            log.info("成功从配置文件加载提示词模板: {}", templatePO.getName());
            return templatePO;

        } catch (IOException e) {
            log.error("读取配置文件失败", e);
            return null;
        } catch (Exception e) {
            log.error("解析配置文件失败", e);
            return null;
        }
    }
}